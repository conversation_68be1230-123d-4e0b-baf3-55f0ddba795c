package com.agricultural.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 商品实体类
 * 对应数据库中的items表
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Item {
    
    // 商品状态常量
    public static final int STATUS_AVAILABLE = 1;  // 上架
    public static final int STATUS_OFFLINE = 2;    // 下架
    public static final int STATUS_OUT_OF_STOCK = 3; // 缺货
    
    // 商品分类常量
    public static final String CATEGORY_VEGETABLE = "蔬菜";
    public static final String CATEGORY_FRUIT = "水果";
    public static final String CATEGORY_GRAIN = "粮食";
    public static final String CATEGORY_OTHER = "其他";
    
    // 商品ID，主键
    private Integer itemId;
    
    // 卖家ID，外键关联用户表
    private Integer sellerId;
    
    // 商品标题
    private String title;
    
    // 商品描述
    private String description;
    
    // 商品价格
    private BigDecimal price;
    
    // 商品分类
    private String category;
    
    // 产地
    private String origin;
    
    // 库存数量
    private Integer stock;
    
    // 计量单位
    private String unit;

    // 商品图片路径
    private String imagePath;

    // 商品状态：1-上架，2-下架，3-缺货
    private Integer status;
    
    // 发布时间
    private Timestamp createTime;
    
    // 更新时间
    private Timestamp updateTime;
    
    // 卖家信息（关联查询时使用）
    private User seller;
    
    /**
     * 无参构造函数
     */
    public Item() {
    }
    
    /**
     * 带参构造函数（用于发布商品）
     * 
     * @param sellerId 卖家ID
     * @param title 商品标题
     * @param description 商品描述
     * @param price 商品价格
     */
    public Item(Integer sellerId, String title, String description, BigDecimal price) {
        this.sellerId = sellerId;
        this.title = title;
        this.description = description;
        this.price = price;
        this.category = CATEGORY_OTHER; // 默认分类
        this.stock = 1; // 默认库存
        this.unit = "件"; // 默认单位
        this.status = STATUS_AVAILABLE; // 默认上架状态
    }
    
    // Getter和Setter方法
    
    public Integer getItemId() {
        return itemId;
    }
    
    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }
    
    public Integer getSellerId() {
        return sellerId;
    }
    
    public void setSellerId(Integer sellerId) {
        this.sellerId = sellerId;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getOrigin() {
        return origin;
    }
    
    public void setOrigin(String origin) {
        this.origin = origin;
    }
    
    public Integer getStock() {
        return stock;
    }
    
    public void setStock(Integer stock) {
        this.stock = stock;
    }
    
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Timestamp getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }
    
    public Timestamp getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }
    
    public User getSeller() {
        return seller;
    }
    
    public void setSeller(User seller) {
        this.seller = seller;
    }
    
    /**
     * 获取状态描述
     * 
     * @return 状态描述字符串
     */
    public String getStatusText() {
        switch (status) {
            case STATUS_AVAILABLE:
                return "上架中";
            case STATUS_OFFLINE:
                return "已下架";
            case STATUS_OUT_OF_STOCK:
                return "缺货";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 检查商品是否可以购买
     * 
     * @return true表示可以购买，false表示不可购买
     */
    public boolean isAvailable() {
        return STATUS_AVAILABLE == status && stock != null && stock > 0;
    }
    
    /**
     * 检查商品是否缺货
     * 
     * @return true表示缺货，false表示有库存
     */
    public boolean isOutOfStock() {
        return STATUS_OUT_OF_STOCK == status || stock == null || stock <= 0;
    }
    
    /**
     * 减少库存
     * 
     * @param quantity 减少的数量
     * @return 减少成功返回true，库存不足返回false
     */
    public boolean reduceStock(int quantity) {
        if (stock == null || stock < quantity) {
            return false;
        }
        stock -= quantity;
        if (stock <= 0) {
            status = STATUS_OUT_OF_STOCK;
        }
        return true;
    }
    
    /**
     * 增加库存
     * 
     * @param quantity 增加的数量
     */
    public void addStock(int quantity) {
        if (stock == null) {
            stock = 0;
        }
        stock += quantity;
        if (stock > 0 && status == STATUS_OUT_OF_STOCK) {
            status = STATUS_AVAILABLE;
        }
    }
    
    /**
     * 重写toString方法，方便调试
     */
    @Override
    public String toString() {
        return "Item{" +
                "itemId=" + itemId +
                ", sellerId=" + sellerId +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", price=" + price +
                ", category='" + category + '\'' +
                ", origin='" + origin + '\'' +
                ", stock=" + stock +
                ", unit='" + unit + '\'' +
                ", imagePath='" + imagePath + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
    
    /**
     * 重写equals方法，用于对象比较
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Item item = (Item) obj;
        return itemId != null ? itemId.equals(item.itemId) : item.itemId == null;
    }
    
    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return itemId != null ? itemId.hashCode() : 0;
    }
}
