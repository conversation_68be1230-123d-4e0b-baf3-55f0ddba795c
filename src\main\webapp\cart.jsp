<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.agricultural.model.User" %>
<%
    User currentUser = (User) session.getAttribute("currentUser");
    if (currentUser == null) {
        response.sendRedirect("login.jsp");
        return;
    }

    // 检查是否为买家
    if (!currentUser.isBuyer()) {
        response.sendRedirect("index.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车 - 农产品电商平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255,255,255,0.2);
        }

        .cart-icon {
            position: relative;
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-info {
            display: none;
            align-items: center;
            gap: 10px;
        }

        .user-name {
            font-weight: bold;
        }

        .auth-links {
            display: flex;
            gap: 10px;
        }

        .credit-score {
            background-color: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .cart-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .cart-items {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .cart-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .item-image {
            width: 80px;
            height: 80px;
            background: #f0f0f0;
            border-radius: 8px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
        }

        .item-info {
            flex: 1;
            margin-right: 15px;
        }

        .item-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .item-details {
            color: #666;
            font-size: 0.9rem;
        }

        .item-price {
            font-size: 1.2rem;
            color: #e74c3c;
            font-weight: bold;
            margin-right: 15px;
        }

        .quantity-control {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quantity-input {
            width: 50px;
            height: 30px;
            text-align: center;
            border: 1px solid #ddd;
            border-left: none;
            border-right: none;
        }

        .remove-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
        }

        .cart-summary {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .total-row {
            font-size: 1.2rem;
            font-weight: bold;
            color: #e74c3c;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }

        .checkout-btn {
            width: 100%;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px;
            font-size: 1.1rem;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 20px;
        }

        .checkout-btn:hover {
            background: #45a049;
        }

        .empty-cart {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-cart i {
            font-size: 4rem;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="logo">🌱 农产品电商平台</div>
            <div class="nav-links">
                <a href="index.jsp">首页</a>
                <% if (currentUser != null) { %>
                    <% if (currentUser.isBuyer()) { %>
                        <!-- 买家导航 -->
                        <a href="cart.jsp" class="cart-icon">
                            🛒 购物车
                            <span class="cart-count" id="cartCount">0</span>
                        </a>
                        <a href="my-orders.jsp">我的订单</a>
                    <% } else if (currentUser.isSeller()) { %>
                        <!-- 卖家导航 -->
                        <a href="my-items.jsp">我的商品</a>
                        <a href="publish-item.jsp">发布商品</a>
                        <a href="my-orders.jsp">我的订单</a>
                    <% } else if (currentUser.isAdmin()) { %>
                        <!-- 管理员导航 -->
                        <a href="admin-orders.jsp">所有订单管理</a>
                    <% } %>
                <% } %>
            </div>
            <div class="user-section">
                <% if (currentUser == null) { %>
                    <!-- 未登录时显示 -->
                    <div class="auth-links">
                        <a href="login.jsp" class="btn btn-primary btn-small">登录</a>
                        <a href="register.jsp" class="btn btn-success btn-small">注册</a>
                    </div>
                <% } else { %>
                    <!-- 已登录时显示 -->
                    <div class="user-info" style="display: flex;">
                        <span class="user-name"><%= currentUser.getUsername() %></span>
                        <%--<span class="credit-score"><%= currentUser.getCreditScore() %>分</span>--%>
                        <a href="javascript:logout()" class="btn btn-primary btn-small">退出</a>
                    </div>
                <% } %>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="cart-header">
            <h2>我的购物车</h2>
        </div>

        <div id="cartContainer">
            <!-- 购物车内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <script>
        // 页面加载时获取购物车数据
        document.addEventListener('DOMContentLoaded', function() {
            loadCartItems();
            // 定期验证库存（每30秒）
            setInterval(validateCartStock, 30000);
        });

        // 加载购物车商品
        function loadCartItems() {
            fetch('<%=request.getContextPath()%>/cart/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCartItems(data.data);
                    } else {
                        showEmptyCart();
                    }
                })
                .catch(error => {
                    console.error('加载购物车失败:', error);
                    showEmptyCart();
                });
        }

        // 显示购物车商品
        function displayCartItems(items) {
            const container = document.getElementById('cartContainer');

            if (!items || items.length === 0) {
                showEmptyCart();
                return;
            }

            let html = '<div class="cart-items">';
            let totalAmount = 0;

            items.forEach(item => {
                // 安全地获取商品信息，处理可能的null值
                const itemInfo = item.item || {};
                const title = escapeHtml(itemInfo.title || '商品信息不可用');
                const price = itemInfo.price || 0;
                const origin = escapeHtml(itemInfo.origin || '未知');
                const category = escapeHtml(itemInfo.category || '未分类');
                const unit = escapeHtml(itemInfo.unit || '件');
                const quantity = item.quantity || 1;

                const subtotal = price * quantity;
                totalAmount += subtotal;

                html += '<div class="cart-item" data-cart-id="' + item.cartId + '">';
                html += '<div class="item-image">🥬</div>';
                html += '<div class="item-info">';
                html += '<div class="item-title">' + title + '</div>';
                html += '<div class="item-details">';
                html += '产地：' + origin + ' | ';
                html += '分类：' + category + ' | ';
                html += '单位：' + unit;
                html += '</div>';
                html += '</div>';
                html += '<div class="item-price">¥' + price.toFixed(2) + '</div>';
                html += '<div class="quantity-control">';
                html += '<button class="quantity-btn" onclick="updateQuantity(' + item.cartId + ', ' + (quantity - 1) + ')">-</button>';
                html += '<input type="text" class="quantity-input" value="' + quantity + '" readonly>';
                html += '<button class="quantity-btn" onclick="updateQuantity(' + item.cartId + ', ' + (quantity + 1) + ')">+</button>';
                html += '</div>';
                html += '<button class="remove-btn" onclick="removeItem(' + item.cartId + ')">删除</button>';
                html += '</div>';
            });

            html += '</div>';

            // 添加结算区域
            html += '<div class="cart-summary">';
            html += '<div class="summary-row">';
            html += '<span>商品总数：</span>';
            html += '<span>' + items.length + ' 种</span>';
            html += '</div>';
            html += '<div class="summary-row total-row">';
            html += '<span>总计：</span>';
            html += '<span>¥' + totalAmount.toFixed(2) + '</span>';
            html += '</div>';
            html += '<button class="checkout-btn" onclick="checkout()">去结算</button>';
            html += '</div>';

            container.innerHTML = html;
        }

        // 显示空购物车
        function showEmptyCart() {
            const container = document.getElementById('cartContainer');
            let html = '<div class="empty-cart">';
            html += '<div style="font-size: 4rem; margin-bottom: 20px;">🛒</div>';
            html += '<h3>购物车是空的</h3>';
            html += '<p>快去挑选一些新鲜的农产品吧！</p>';
            html += '<a href="index.jsp" style="display: inline-block; margin-top: 20px; padding: 10px 20px; background: #4CAF50; color: white; text-decoration: none; border-radius: 4px;">去购物</a>';
            html += '</div>';
            container.innerHTML = html;
        }

        // 更新商品数量
        function updateQuantity(cartId, newQuantity) {
            if (newQuantity < 1) {
                removeItem(cartId);
                return;
            }

            // 先更新数量，然后验证库存
            fetch('<%=request.getContextPath()%>/cart/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'cartId=' + cartId + '&quantity=' + newQuantity
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadCartItems(); // 重新加载购物车
                    // 验证库存并提示用户
                    setTimeout(function() {
                        validateCartStock(function(isValid, errors) {
                            if (!isValid && errors.length > 0) {
                                alert('提醒：\n' + errors.join('\n'));
                            }
                        });
                    }, 500);
                } else {
                    alert('更新失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('更新数量失败:', error);
                alert('更新失败，请重试');
            });
        }

        // 删除商品
        function removeItem(cartId) {
            if (!confirm('确定要删除这个商品吗？')) {
                return;
            }

            fetch('<%=request.getContextPath()%>/cart/remove', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'cartId=' + cartId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadCartItems(); // 重新加载购物车
                } else {
                    alert('删除失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('删除商品失败:', error);
                alert('删除失败，请重试');
            });
        }

        // 结算
        function checkout() {
            // 先验证库存
            validateCartStock(function(isValid, errors) {
                if (!isValid) {
                    alert('购物车中有商品库存不足或不可购买：\n' + errors.join('\n') + '\n\n请修改数量或移除相关商品后重试。');
                    loadCartItems(); // 重新加载购物车以显示最新状态
                    return;
                }

                if (confirm('确定要提交订单吗？')) {
                    window.location.href = 'checkout.jsp';
                }
            });
        }

        // 验证购物车库存
        function validateCartStock(callback) {
            fetch('<%=request.getContextPath()%>/cart/validate')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const result = data.data;
                        if (callback) {
                            callback(result.valid, result.errors || []);
                        }
                    } else {
                        console.error('验证库存失败:', data.message);
                        if (callback) {
                            callback(false, ['验证库存失败，请稍后重试']);
                        }
                    }
                })
                .catch(error => {
                    console.error('验证库存失败:', error);
                    if (callback) {
                        callback(false, ['验证库存失败，请稍后重试']);
                    }
                });
        }

        // 工具函数：HTML转义
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('<%=request.getContextPath()%>/user/logout')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.href = 'index.jsp';
                        } else {
                            alert('退出失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('退出失败:', error);
                        alert('退出失败，请稍后重试');
                    });
            }
        }

        // 更新购物车数量
        function updateCartCount() {
            <% if (currentUser != null) { %>
            fetch('<%=request.getContextPath()%>/cart/count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const cartCount = document.getElementById('cartCount');
                        if (cartCount) {
                            cartCount.textContent = data.data;
                            cartCount.style.display = data.data > 0 ? 'flex' : 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error('获取购物车数量失败:', error);
                });
            <% } %>
        }

        // 页面加载时更新购物车数量
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
        });
    </script>
</body>
</html>
