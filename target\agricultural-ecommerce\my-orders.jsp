<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.agricultural.model.User" %>
<%
    // 检查是否已登录
    User currentUser = (User) session.getAttribute("currentUser");
    if (currentUser == null) {
        response.sendRedirect("login.jsp?redirect=" + java.net.URLEncoder.encode(request.getRequestURL().toString(), "UTF-8"));
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的订单 - 农产品电商平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .cart-icon {
            position: relative;
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-info {
            display: none;
            align-items: center;
            gap: 10px;
        }

        .user-name {
            font-weight: bold;
        }

        .auth-links {
            display: flex;
            gap: 10px;
        }

        .credit-score {
            background-color: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .page-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            background: white;
            border: none;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .tab.active {
            background-color: #3498db;
            color: white;
        }

        .tab:hover:not(.active) {
            background-color: #ecf0f1;
        }

        .orders-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .order-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s;
        }

        .order-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .order-id {
            font-weight: bold;
            color: #2c3e50;
        }

        .order-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-pending {
            background-color: #f39c12;
            color: white;
        }

        .status-paid {
            background-color: #3498db;
            color: white;
        }

        .status-shipped {
            background-color: #9b59b6;
            color: white;
        }

        .status-completed {
            background-color: #27ae60;
            color: white;
        }

        .status-cancelled {
            background-color: #e74c3c;
            color: white;
        }

        .order-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .order-info {
            flex: 1;
        }

        .item-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .order-meta {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .order-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #e74c3c;
        }

        .order-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }

        .rating-group {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .rating-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .rating-item input[type="radio"] {
            margin: 0;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                gap: 10px;
            }

            .tabs {
                flex-direction: column;
            }

            .order-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .order-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .modal-content {
                margin: 5% auto;
                width: 95%;
            }

            .rating-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <nav class="nav">
            <div class="logo">🌱 农产品电商平台</div>
            <div class="nav-links">
                <a href="index.jsp">首页</a>
                <% if (currentUser != null) { %>
                    <% if (currentUser.isBuyer()) { %>
                        <!-- 买家导航 -->
                        <a href="cart.jsp" class="cart-icon">
                            🛒 购物车
                            <span class="cart-count" id="cartCount">0</span>
                        </a>
                        <a href="my-orders.jsp">我的订单</a>
                    <% } else if (currentUser.isSeller()) { %>
                        <!-- 卖家导航 -->
                        <a href="my-items.jsp">我的商品</a>
                        <a href="publish-item.jsp">发布商品</a>
                        <a href="my-orders.jsp">我的订单</a>
                    <% } else if (currentUser.isAdmin()) { %>
                        <!-- 管理员导航 -->
                        <a href="admin-orders.jsp">所有订单管理</a>
                    <% } %>
                <% } %>
            </div>
            <div class="user-section">
                <% if (currentUser == null) { %>
                    <!-- 未登录时显示 -->
                    <div class="auth-links">
                        <a href="login.jsp" class="btn btn-primary btn-small">登录</a>
                        <a href="register.jsp" class="btn btn-success btn-small">注册</a>
                    </div>
                <% } else { %>
                    <!-- 已登录时显示 -->
                    <div class="user-info" style="display: flex;">
                        <span class="user-name"><%= currentUser.getUsername() %></span>
                        <%--<span class="credit-score"><%= currentUser.getCreditScore() %>分</span>--%>
                        <a href="javascript:logout()" class="btn btn-primary btn-small">退出</a>
                    </div>
                <% } %>
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main class="container">
        <h1 class="page-title">我的订单</h1>

        <!-- 标签页 -->
        <div class="tabs">
            <% if (currentUser.isBuyer()) { %>
                <!-- 买家标签 -->
                <button class="tab active" onclick="switchTab('all', this)">全部订单</button>
                <button class="tab" onclick="switchTab('buy', this)">我的购买</button>
            <% } else if (currentUser.isSeller()) { %>
                <!-- 卖家标签 -->
                <button class="tab active" onclick="switchTab('all', this)">全部订单</button>
                <button class="tab" onclick="switchTab('sell', this)">我的销售</button>
            <% } else { %>
                <!-- 管理员或其他角色 -->
                <button class="tab active" onclick="switchTab('all', this)">全部订单</button>
            <% } %>
        </div>

        <!-- 订单列表 -->
        <div class="orders-container">
            <div id="ordersContent">
                <div class="loading">正在加载订单...</div>
            </div>
        </div>
    </main>

    <!-- 发货模态框 -->
    <div id="shipModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">发货信息</h3>
                <span class="close" onclick="closeShipModal()">&times;</span>
            </div>
            <form onsubmit="event.preventDefault(); shipOrder();">
                <input type="hidden" id="shipOrderId" />
                <div class="form-group">
                    <label class="form-label" for="logisticsCompany">物流公司</label>
                    <input type="text" id="logisticsCompany" class="form-input" placeholder="请输入物流公司名称" value="顺丰快递" required />
                </div>
                <div class="form-group">
                    <label class="form-label" for="trackingNumber">物流单号</label>
                    <input type="text" id="trackingNumber" class="form-input" placeholder="请输入物流单号" required />
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeShipModal()">取消</button>
                    <button type="submit" class="btn btn-primary">确认发货</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 评价模态框 -->
    <div id="reviewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">订单评价</h3>
                <span class="close" onclick="closeReviewModal()">&times;</span>
            </div>
            <form onsubmit="event.preventDefault(); submitReview();">
                <input type="hidden" id="reviewOrderId" />
                <div class="form-group">
                    <label class="form-label">评分</label>
                    <div class="rating-group">
                        <div class="rating-item">
                            <input type="radio" id="score1" name="score" value="1" />
                            <label for="score1">1分 (很差)</label>
                        </div>
                        <div class="rating-item">
                            <input type="radio" id="score2" name="score" value="2" />
                            <label for="score2">2分 (较差)</label>
                        </div>
                        <div class="rating-item">
                            <input type="radio" id="score3" name="score" value="3" />
                            <label for="score3">3分 (一般)</label>
                        </div>
                        <div class="rating-item">
                            <input type="radio" id="score4" name="score" value="4" />
                            <label for="score4">4分 (良好)</label>
                        </div>
                        <div class="rating-item">
                            <input type="radio" id="score5" name="score" value="5" />
                            <label for="score5">5分 (优秀)</label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="reviewComment">评价内容</label>
                    <textarea id="reviewComment" class="form-textarea" placeholder="请输入您的评价内容（可选）"></textarea>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeReviewModal()">取消</button>
                    <button type="submit" class="btn btn-success">提交评价</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentTab = 'all';
        const currentUserId = <%= currentUser.getUserId() %>;

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadOrders(currentTab);
            updateCartCount();
        });

        // 切换标签页
        function switchTab(tab, element) {
            currentTab = tab;

            // 更新标签页样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            element.classList.add('active');

            // 加载对应的订单
            loadOrders(tab);
        }

        // 加载订单列表
        function loadOrders(type) {
            document.getElementById('ordersContent').innerHTML = '<div class="loading">正在加载订单...</div>';

            let url = '<%=request.getContextPath()%>/order/';
            if (type === 'buy') {
                url = '<%=request.getContextPath()%>/order/buy';
            } else if (type === 'sell') {
                url = '<%=request.getContextPath()%>/order/sell';
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayOrders(data.data, type);
                    } else {
                        document.getElementById('ordersContent').innerHTML = '<div class="empty">加载订单失败: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载订单失败:', error);
                    document.getElementById('ordersContent').innerHTML = '<div class="empty">加载订单失败，请稍后重试</div>';
                });
        }

        // 显示订单列表
        function displayOrders(orders, type) {
            const container = document.getElementById('ordersContent');

            if (!orders || orders.length === 0) {
                container.innerHTML = '<div class="empty">暂无订单</div>';
                return;
            }

            const ordersHtml = orders.map(order => {
                const statusClass = getStatusClass(order.status);
                const statusText = getStatusText(order.status);
                const isMyBuy = order.buyerId === currentUserId;

                let html = '<div class="order-card">';
                html += '<div class="order-header">';
                html += '<div class="order-id">订单号: ' + order.orderId + '</div>';
                html += '<div class="order-status ' + statusClass + '">' + statusText + '</div>';
                html += '</div>';
                html += '<div class="order-content">';
                html += '<div class="order-info">';

                // 显示订单商品信息
                let itemsInfo = '';
                if (order.orderItems && order.orderItems.length > 0) {
                    if (order.orderItems.length === 1) {
                        // 单个商品
                        const item = order.orderItems[0].item;
                        itemsInfo = escapeHtml(item ? item.title : '商品信息不可用');
                    } else {
                        // 多个商品
                        itemsInfo = order.orderItems.length + '件商品';
                    }
                } else {
                    itemsInfo = '商品信息不可用';
                }
                html += '<div class="item-title">' + itemsInfo + '</div>';

                html += '<div class="order-meta">';
                html += (type === 'buy' ? '卖家' : type === 'sell' ? '买家' : (isMyBuy ? '卖家' : '买家')) + ': ';
                html += escapeHtml(type === 'buy' ? (order.seller ? order.seller.username : '未知') :
                                   type === 'sell' ? (order.buyer ? order.buyer.username : '未知') :
                                   (isMyBuy ? (order.seller ? order.seller.username : '未知') :
                                             (order.buyer ? order.buyer.username : '未知')));
                html += '</div>';
                html += '<div class="order-meta">下单时间: ' + formatDate(order.createTime) + '</div>';
                html += '<div class="order-price">¥' + order.totalPrice + '</div>';
                html += '</div>';
                html += '<div class="order-actions">';
                html += getOrderActions(order, type, isMyBuy);
                html += '</div>';
                html += '</div>';
                html += '</div>';
                return html;
            }).join('');

            container.innerHTML = ordersHtml;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 1: return 'status-pending';
                case 2: return 'status-paid';
                case 3: return 'status-shipped';
                case 4: return 'status-completed';
                case 5: return 'status-cancelled';
                default: return '';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 1: return '待支付';
                case 2: return '已支付';
                case 3: return '已发货';
                case 4: return '已完成';
                case 5: return '已取消';
                default: return '未知状态';
            }
        }

        // 获取订单操作按钮
        function getOrderActions(order, type, isMyBuy) {
            let actions = [];

            // 根据订单状态和用户角色显示不同操作
            if (order.status === 1) { // 待支付
                if (type === 'buy' || isMyBuy) {
                    // 买家可以支付订单
                    actions.push('<button class="btn btn-success" onclick="payOrder(' + order.orderId + ')">立即支付</button>');
                }
                // 买家和卖家都可以取消订单
                actions.push('<button class="btn btn-danger" onclick="cancelOrder(' + order.orderId + ')">取消订单</button>');
            } else if (order.status === 2) { // 已支付
                if (type === 'sell' || !isMyBuy) {
                    // 卖家可以发货
                    actions.push('<button class="btn btn-primary" onclick="showShipModal(' + order.orderId + ')">发货</button>');
                }
            } else if (order.status === 3) { // 已发货
                if (type === 'buy' || isMyBuy) {
                    // 买家可以确认收货
                    actions.push('<button class="btn btn-success" onclick="confirmOrder(' + order.orderId + ')">确认收货</button>');
                }
            } else if (order.status === 4) { // 已完成
                if (type === 'buy' || isMyBuy) {
                    // 买家可以评价，但需要检查是否已评价
                    actions.push('<button class="btn btn-success" onclick="showReviewModal(' + order.orderId + ')" id="reviewBtn_' + order.orderId + '">评价</button>');
                    // 异步检查是否已评价
                    checkIfReviewed(order.orderId);
                }
            }

            return actions.join('');
        }

        // 支付订单
        function payOrder(orderId) {
            if (confirm('确认支付订单？（模拟支付）')) {
                fetch('<%=request.getContextPath()%>/order/pay', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'orderId=' + orderId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('支付成功！');
                        loadOrders(currentTab);
                    } else {
                        alert('支付失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('支付失败:', error);
                    alert('支付失败，请稍后重试');
                });
            }
        }

        // 检查订单是否已评价
        function checkIfReviewed(orderId) {
            fetch('<%=request.getContextPath()%>/review/order/' + orderId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 已评价，隐藏评价按钮
                        const reviewBtn = document.getElementById('reviewBtn_' + orderId);
                        if (reviewBtn) {
                            reviewBtn.style.display = 'none';
                        }
                    }
                })
                .catch(error => {
                    // 忽略错误，保持按钮显示
                });
        }

        // 显示发货模态框
        function showShipModal(orderId) {
            document.getElementById('shipOrderId').value = orderId;
            document.getElementById('shipModal').style.display = 'block';
        }

        // 发货
        function shipOrder() {
            const orderId = document.getElementById('shipOrderId').value;
            const logisticsCompany = document.getElementById('logisticsCompany').value.trim();
            const trackingNumber = document.getElementById('trackingNumber').value.trim();

            if (!logisticsCompany) {
                alert('请输入物流公司');
                return;
            }
            if (!trackingNumber) {
                alert('请输入物流单号');
                return;
            }

            fetch('<%=request.getContextPath()%>/order/ship', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'orderId=' + orderId + '&logisticsCompany=' + encodeURIComponent(logisticsCompany) + '&trackingNumber=' + encodeURIComponent(trackingNumber)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('发货成功！');
                    closeShipModal();
                    loadOrders(currentTab);
                } else {
                    alert('发货失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('发货失败:', error);
                alert('发货失败，请稍后重试');
            });
        }

        // 关闭发货模态框
        function closeShipModal() {
            document.getElementById('shipModal').style.display = 'none';
            document.getElementById('logisticsCompany').value = '';
            document.getElementById('trackingNumber').value = '';
        }

        // 确认收货
        function confirmOrder(orderId) {
            if (confirm('确认收货？')) {
                fetch('<%=request.getContextPath()%>/order/confirm', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'orderId=' + orderId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('确认收货成功！');
                        loadOrders(currentTab);
                    } else {
                        alert('确认收货失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('确认收货失败:', error);
                    alert('确认收货失败，请稍后重试');
                });
            }
        }

        // 取消订单
        function cancelOrder(orderId) {
            if (confirm('确定要取消这个订单吗？')) {
                fetch('<%=request.getContextPath()%>/order/cancel', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'orderId=' + orderId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('订单取消成功！');
                        loadOrders(currentTab);
                    } else {
                        alert('取消失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('取消订单失败:', error);
                    alert('取消失败，请稍后重试');
                });
            }
        }

        // 显示评价模态框
        function showReviewModal(orderId) {
            document.getElementById('reviewOrderId').value = orderId;
            document.getElementById('reviewModal').style.display = 'block';
        }

        // 提交评价
        function submitReview() {
            const orderId = document.getElementById('reviewOrderId').value;
            const score = document.querySelector('input[name="score"]:checked');
            const comment = document.getElementById('reviewComment').value.trim();

            if (!score) {
                alert('请选择评分');
                return;
            }

            fetch('<%=request.getContextPath()%>/review/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'orderId=' + orderId + '&score=' + score.value + '&comment=' + encodeURIComponent(comment)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('评价提交成功！');
                    closeReviewModal();
                    loadOrders(currentTab);
                } else {
                    alert('评价失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('提交评价失败:', error);
                alert('评价失败，请稍后重试');
            });
        }

        // 关闭评价模态框
        function closeReviewModal() {
            document.getElementById('reviewModal').style.display = 'none';
            document.querySelectorAll('input[name="score"]').forEach(radio => radio.checked = false);
            document.getElementById('reviewComment').value = '';
        }

        // 工具函数：HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 工具函数：格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 更新购物车数量
        function updateCartCount() {
            <% if (currentUser != null) { %>
            fetch('<%=request.getContextPath()%>/cart/count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const cartCount = document.getElementById('cartCount');
                        if (cartCount) {
                            cartCount.textContent = data.data;
                            cartCount.style.display = data.data > 0 ? 'flex' : 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error('获取购物车数量失败:', error);
                });
            <% } %>
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('<%=request.getContextPath()%>/user/logout')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.href = 'index.jsp';
                        } else {
                            alert('退出失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('退出失败:', error);
                        alert('退出失败，请稍后重试');
                    });
            }
        }
    </script>
</body>
</html>
