<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.agricultural.model.User" %>
<%
    // 检查是否已登录
    User currentUser = (User) session.getAttribute("currentUser");
    if (currentUser == null) {
        response.sendRedirect("login.jsp?redirect=" + java.net.URLEncoder.encode(request.getRequestURL().toString(), "UTF-8"));
        return;
    }

    // 检查是否为管理员
    if (!currentUser.isAdmin()) {
        response.sendRedirect("index.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>所有订单管理 - 农产品电商平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-name {
            font-weight: bold;
        }

        .credit-score {
            background-color: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 14px;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .page-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-label {
            font-weight: bold;
            color: #333;
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .orders-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .order-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .order-table th,
        .order-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .order-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }

        .order-table tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-pending {
            background-color: #f39c12;
            color: white;
        }

        .status-paid {
            background-color: #3498db;
            color: white;
        }

        .status-shipped {
            background-color: #9b59b6;
            color: white;
        }

        .status-completed {
            background-color: #27ae60;
            color: white;
        }

        .status-cancelled {
            background-color: #e74c3c;
            color: white;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                gap: 10px;
            }

            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .order-table {
                font-size: 14px;
            }

            .order-table th,
            .order-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <nav class="nav">
            <div class="logo">🌱 农产品电商平台</div>
            <div class="nav-links">
                <a href="index.jsp">首页</a>
                <% if (currentUser != null) { %>
                    <% if (currentUser.isAdmin()) { %>
                        <!-- 管理员导航 -->
                        <a href="admin-orders.jsp">所有订单管理</a>
                    <% } %>
                <% } %>
            </div>
            <div class="user-section">
                <% if (currentUser != null) { %>
                    <!-- 已登录时显示 -->
                    <div class="user-info">
                        <span class="user-name"><%= currentUser.getUsername() %> (管理员)</span>
                        <%--<span class="credit-score"><%= currentUser.getCreditScore() %>分</span>--%>
                        <a href="javascript:logout()" class="btn btn-primary btn-small">退出</a>
                    </div>
                <% } %>
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main class="container">
        <h1 class="page-title">所有订单管理</h1>

        <!-- 统计信息 -->
        <div class="admin-stats">
            <div class="stat-card">
                <div class="stat-number" id="totalOrders">0</div>
                <div class="stat-label">总订单数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingOrders">0</div>
                <div class="stat-label">待支付订单</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="shippedOrders">0</div>
                <div class="stat-label">已发货订单</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="completedOrders">0</div>
                <div class="stat-label">已完成订单</div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label class="filter-label">订单状态</label>
                    <select id="statusFilter" class="filter-input" onchange="filterOrders()">
                        <option value="">全部状态</option>
                        <option value="1">待支付</option>
                        <option value="2">已支付</option>
                        <option value="3">已发货</option>
                        <option value="4">已完成</option>
                        <option value="5">已取消</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">订单号</label>
                    <input type="text" id="orderIdFilter" class="filter-input" placeholder="输入订单号" onchange="filterOrders()">
                </div>
                <div class="filter-group">
                    <label class="filter-label">买家用户名</label>
                    <input type="text" id="buyerFilter" class="filter-input" placeholder="输入买家用户名" onchange="filterOrders()">
                </div>
                <div class="filter-group">
                    <label class="filter-label">卖家用户名</label>
                    <input type="text" id="sellerFilter" class="filter-input" placeholder="输入卖家用户名" onchange="filterOrders()">
                </div>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="orders-container">
            <div id="ordersContent">
                <div class="loading">正在加载订单...</div>
            </div>
        </div>
    </main>

    <script>
        let allOrders = [];
        let filteredOrders = [];

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadAllOrders();
        });

        // 加载所有订单
        function loadAllOrders() {
            fetch('<%=request.getContextPath()%>/order/all')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        allOrders = data.data;
                        filteredOrders = [...allOrders];
                        displayOrders(filteredOrders);
                        updateStatistics(allOrders);
                    } else {
                        document.getElementById('ordersContent').innerHTML = '<div class="empty">加载订单失败: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载订单失败:', error);
                    document.getElementById('ordersContent').innerHTML = '<div class="empty">加载订单失败，请稍后重试</div>';
                });
        }

        // 显示订单列表
        function displayOrders(orders) {
            const container = document.getElementById('ordersContent');

            if (!orders || orders.length === 0) {
                container.innerHTML = '<div class="empty">暂无订单</div>';
                return;
            }

            let html = '<table class="order-table">';
            html += '<thead>';
            html += '<tr>';
            html += '<th>订单号</th>';
            html += '<th>买家</th>';
            html += '<th>卖家</th>';
            html += '<th>商品信息</th>';
            html += '<th>订单金额</th>';
            html += '<th>订单状态</th>';
            html += '<th>收货地址</th>';
            html += '<th>物流信息</th>';
            html += '<th>下单时间</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';

            orders.forEach(order => {
                const statusClass = getStatusClass(order.status);
                const statusText = getStatusText(order.status);

                html += '<tr>';
                html += '<td>' + order.orderId + '</td>';
                html += '<td>' + escapeHtml(order.buyer ? order.buyer.username : '未知') + '</td>';
                html += '<td>' + escapeHtml(order.seller ? order.seller.username : '未知') + '</td>';

                // 商品信息
                let itemsInfo = '';
                if (order.orderItems && order.orderItems.length > 0) {
                    if (order.orderItems.length === 1) {
                        const item = order.orderItems[0].item;
                        itemsInfo = escapeHtml(item ? item.title : '商品信息不可用');
                    } else {
                        itemsInfo = order.orderItems.length + '件商品';
                    }
                } else {
                    itemsInfo = '商品信息不可用';
                }
                html += '<td>' + itemsInfo + '</td>';

                html += '<td>¥' + order.totalPrice + '</td>';
                html += '<td><span class="status-badge ' + statusClass + '">' + statusText + '</span></td>';
                html += '<td>' + escapeHtml(order.shippingAddress || '无') + '</td>';

                // 物流信息
                let logisticsInfo = '无';
                if (order.logisticsCompany && order.trackingNumber) {
                    logisticsInfo = escapeHtml(order.logisticsCompany) + '<br>' + escapeHtml(order.trackingNumber);
                }
                html += '<td>' + logisticsInfo + '</td>';

                html += '<td>' + formatDate(order.createTime) + '</td>';
                html += '</tr>';
            });

            html += '</tbody>';
            html += '</table>';

            container.innerHTML = html;
        }

        // 更新统计信息
        function updateStatistics(orders) {
            const stats = {
                total: orders.length,
                pending: 0,
                shipped: 0,
                completed: 0
            };

            orders.forEach(order => {
                switch (order.status) {
                    case 1:
                        stats.pending++;
                        break;
                    case 3:
                        stats.shipped++;
                        break;
                    case 4:
                        stats.completed++;
                        break;
                }
            });

            document.getElementById('totalOrders').textContent = stats.total;
            document.getElementById('pendingOrders').textContent = stats.pending;
            document.getElementById('shippedOrders').textContent = stats.shipped;
            document.getElementById('completedOrders').textContent = stats.completed;
        }

        // 筛选订单
        function filterOrders() {
            const statusFilter = document.getElementById('statusFilter').value;
            const orderIdFilter = document.getElementById('orderIdFilter').value.trim();
            const buyerFilter = document.getElementById('buyerFilter').value.trim().toLowerCase();
            const sellerFilter = document.getElementById('sellerFilter').value.trim().toLowerCase();

            filteredOrders = allOrders.filter(order => {
                // 状态筛选
                if (statusFilter && order.status != statusFilter) {
                    return false;
                }

                // 订单号筛选
                if (orderIdFilter && !order.orderId.toString().includes(orderIdFilter)) {
                    return false;
                }

                // 买家筛选
                if (buyerFilter && (!order.buyer || !order.buyer.username.toLowerCase().includes(buyerFilter))) {
                    return false;
                }

                // 卖家筛选
                if (sellerFilter && (!order.seller || !order.seller.username.toLowerCase().includes(sellerFilter))) {
                    return false;
                }

                return true;
            });

            displayOrders(filteredOrders);
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 1: return 'status-pending';
                case 2: return 'status-paid';
                case 3: return 'status-shipped';
                case 4: return 'status-completed';
                case 5: return 'status-cancelled';
                default: return '';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 1: return '待支付';
                case 2: return '已支付';
                case 3: return '已发货';
                case 4: return '已完成';
                case 5: return '已取消';
                default: return '未知状态';
            }
        }

        // 工具函数：HTML转义
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 工具函数：格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('<%=request.getContextPath()%>/user/logout')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.href = 'index.jsp';
                        } else {
                            alert('退出失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('退出失败:', error);
                        alert('退出失败，请稍后重试');
                    });
            }
        }
    </script>
</body>
</html>
