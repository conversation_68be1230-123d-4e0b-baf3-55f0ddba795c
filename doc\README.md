# 农产品电商平台

## 项目简介
基于JSP/Servlet的农产品电商平台，支持买家购买、卖家管理商品、管理员物流对接等功能。

## 技术栈
- **后端**: JSP/Servlet + MySQL 8 + JDK 8 + Tomcat 8
- **前端**: HTML + CSS + JavaScript
- **数据库连接池**: Apache DBCP2
- **JSON处理**: Gson

## 功能特性

### 用户角色
- **买家**: 浏览商品、加入购物车、下单支付、查看物流、评价
- **卖家**: 发布商品、管理库存、处理订单、查看评价
- **管理员**: 用户管理、物流对接、系统监控

### 核心功能
1. **商品管理**
   - 按分类展示（蔬菜、水果、粮食、其他）
   - 商品详情（价格、产地、库存、单位）
   - 搜索功能

2. **购物车**
   - 添加/删除商品
   - 数量调整
   - 批量结算

3. **订单系统**
   - 订单创建
   - 支付模拟
   - 物流跟踪
   - 订单状态管理

4. **评价系统**
   - 5星评分
   - 文字评价
   - 信誉分计算

## 数据库设计

### 主要表结构
- `users`: 用户表（角色、地址）
- `items`: 商品表（分类、产地、库存）
- `cart_items`: 购物车表
- `orders`: 订单表（物流信息）
- `order_items`: 订单商品表
- `reviews`: 评价表

## 部署说明

### 1. 数据库配置
```sql
-- 执行 sql/database.sql 创建数据库和表
mysql -u root -p < sql/database.sql
```

### 2. 项目配置
在 `web.xml` 中配置数据库连接：
```xml
<context-param>
    <param-name>db.driver</param-name>
    <param-value>com.mysql.cj.jdbc.Driver</param-value>
</context-param>
<context-param>
    <param-name>db.url</param-name>
    <param-value>**************************************************************************************</param-value>
</context-param>
<context-param>
    <param-name>db.username</param-name>
    <param-value>root</param-value>
</context-param>
<context-param>
    <param-name>db.password</param-name>
    <param-value>your_password</param-value>
</context-param>
```

### 3. 编译部署
```bash
mvn clean package
# 将生成的 agricultural-ecommerce.war 部署到 Tomcat
```

## 测试账号
- **管理员**: admin / 123456
- **卖家**: farmer1 / 123456
- **买家**: buyer1 / 123456

## 项目结构
```
src/main/java/com/agricultural/
├── model/          # 实体类
│   ├── User.java
│   ├── Item.java
│   ├── CartItem.java
│   ├── Order.java
│   ├── OrderItem.java
│   └── Review.java
├── dao/            # 数据访问层
├── service/        # 业务逻辑层
├── servlet/        # 控制器层
├── util/           # 工具类
└── filter/         # 过滤器

src/main/webapp/
├── index.jsp       # 首页
├── login.jsp       # 登录页
├── register.jsp    # 注册页
├── cart.jsp        # 购物车页
└── ...
```

## 开发说明
这是一个简化的农产品电商平台，主要用于学习和作业目的。实现了基本的增删改查功能，包括：

- 用户注册登录
- 商品浏览和搜索
- 购物车管理
- 订单处理
- 支付模拟
- 物流跟踪
- 评价系统

## 注意事项
1. 这是一个教学项目，不适用于生产环境
2. 支付功能为模拟实现
3. 物流跟踪为简化版本
4. 建议在本地环境进行测试和学习
