package com.agricultural.servlet;

import com.agricultural.model.CartItem;
import com.agricultural.model.Item;
import com.agricultural.model.User;
import com.agricultural.service.CartService;
import com.agricultural.dao.ItemDAO;
import com.agricultural.util.ResponseUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 购物车控制器
 * 处理购物车相关的HTTP请求
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CartServlet extends HttpServlet {
    
    private CartService cartService;
    
    /**
     * Servlet初始化
     */
    @Override
    public void init() throws ServletException {
        super.init();
        this.cartService = new CartService();
        System.out.println("CartServlet initialized");
    }
    
    /**
     * 处理GET请求
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo == null || "/".equals(pathInfo) || "/list".equals(pathInfo)) {
                // 获取购物车商品列表
                getCartItems(request, response);
            } else if ("/count".equals(pathInfo)) {
                // 获取购物车商品数量
                getCartItemCount(request, response);
            } else if ("/summary".equals(pathInfo)) {
                // 获取购物车摘要信息
                getCartSummary(request, response);
            } else if ("/validate".equals(pathInfo)) {
                // 验证购物车商品库存
                validateCartStock(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理POST请求
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/add".equals(pathInfo)) {
                // 添加商品到购物车
                addToCart(request, response);
            } else if ("/update".equals(pathInfo)) {
                // 更新购物车商品数量
                updateCartItem(request, response);
            } else if ("/remove".equals(pathInfo)) {
                // 删除购物车商品
                removeFromCart(request, response);
            } else if ("/clear".equals(pathInfo)) {
                // 清空购物车
                clearCart(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取购物车商品列表
     */
    private void getCartItems(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (!checkBuyerPermission(currentUser, response)) {
            return;
        }
        
        try {
            List<CartItem> cartItems = cartService.getCartItems(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, cartItems);
        } catch (Exception e) {
            System.err.println("获取购物车商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取购物车商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取购物车商品数量
     */
    private void getCartItemCount(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        try {
            int count = cartService.getCartItemCount(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, count);
        } catch (Exception e) {
            System.err.println("获取购物车商品数量失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取购物车商品数量失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取购物车摘要信息
     */
    private void getCartSummary(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            CartService.CartSummary summary = cartService.getCartSummary(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, summary);
        } catch (Exception e) {
            System.err.println("获取购物车摘要失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取购物车摘要失败: " + e.getMessage());
        }
    }

    /**
     * 验证购物车商品库存
     */
    private void validateCartStock(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            List<CartItem> cartItems = cartService.getCartItems(currentUser.getUserId());
            List<String> errors = new ArrayList<>();
            boolean hasStockIssues = false;
            ItemDAO itemDAO = new ItemDAO();

            for (CartItem cartItem : cartItems) {
                if (cartItem.getItem() == null) {
                    errors.add("购物车中存在无效商品，请刷新后重试");
                    hasStockIssues = true;
                    continue;
                }

                // 重新获取最新商品信息
                Item latestItem = itemDAO.findById(cartItem.getItem().getItemId());
                if (latestItem == null) {
                    errors.add("商品 " + cartItem.getItem().getTitle() + " 已不存在");
                    hasStockIssues = true;
                    continue;
                }

                // 检查商品状态和库存
                if (!latestItem.isAvailable()) {
                    if (latestItem.getStatus() == Item.STATUS_OFFLINE) {
                        errors.add("商品 " + latestItem.getTitle() + " 已下架");
                    } else if (latestItem.getStatus() == Item.STATUS_OUT_OF_STOCK) {
                        errors.add("商品 " + latestItem.getTitle() + " 已缺货");
                    } else {
                        errors.add("商品 " + latestItem.getTitle() + " 不可购买");
                    }
                    hasStockIssues = true;
                } else if (latestItem.getStock() == null || latestItem.getStock() < cartItem.getQuantity()) {
                    errors.add("商品 " + latestItem.getTitle() + " 库存不足，当前库存：" +
                        (latestItem.getStock() != null ? latestItem.getStock() : 0) + "件");
                    hasStockIssues = true;
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("valid", !hasStockIssues);
            result.put("errors", errors);

            ResponseUtil.sendJsonSuccess(response, result);
        } catch (Exception e) {
            System.err.println("验证购物车库存失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "验证购物车库存失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加商品到购物车
     */
    private void addToCart(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (!checkBuyerPermission(currentUser, response)) {
            return;
        }
        
        String itemIdStr = request.getParameter("itemId");
        String quantityStr = request.getParameter("quantity");
        
        // 参数验证
        if (itemIdStr == null || itemIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品ID不能为空");
            return;
        }
        
        try {
            Integer itemId = Integer.parseInt(itemIdStr);
            Integer quantity = 1; // 默认数量为1
            
            if (quantityStr != null && !quantityStr.trim().isEmpty()) {
                quantity = Integer.parseInt(quantityStr);
                if (quantity <= 0) {
                    ResponseUtil.sendJsonError(response, "商品数量必须大于0");
                    return;
                }
            }
            
            // 添加到购物车
            Integer cartId = cartService.addToCart(currentUser.getUserId(), itemId, quantity);
            
            if (cartId != null) {
                ResponseUtil.sendJsonSuccess(response, "添加成功", cartId);
            } else {
                ResponseUtil.sendJsonError(response, "添加失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "商品ID或数量格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("添加购物车失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "添加购物车失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新购物车商品数量
     */
    private void updateCartItem(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (!checkBuyerPermission(currentUser, response)) {
            return;
        }
        
        String cartIdStr = request.getParameter("cartId");
        String quantityStr = request.getParameter("quantity");
        
        // 参数验证
        if (cartIdStr == null || cartIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "购物车ID不能为空");
            return;
        }
        if (quantityStr == null || quantityStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品数量不能为空");
            return;
        }
        
        try {
            Integer cartId = Integer.parseInt(cartIdStr);
            Integer quantity = Integer.parseInt(quantityStr);
            
            if (quantity <= 0) {
                ResponseUtil.sendJsonError(response, "商品数量必须大于0");
                return;
            }
            
            // 更新购物车商品数量
            boolean success = cartService.updateQuantity(cartId, quantity);
            
            if (success) {
                ResponseUtil.sendJsonSuccess(response, "更新成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "更新失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "购物车ID或数量格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("更新购物车失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "更新购物车失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除购物车商品
     */
    private void removeFromCart(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (!checkBuyerPermission(currentUser, response)) {
            return;
        }
        
        String cartIdStr = request.getParameter("cartId");
        
        // 参数验证
        if (cartIdStr == null || cartIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "购物车ID不能为空");
            return;
        }
        
        try {
            Integer cartId = Integer.parseInt(cartIdStr);
            
            // 删除购物车商品
            boolean success = cartService.removeFromCart(cartId);
            
            if (success) {
                ResponseUtil.sendJsonSuccess(response, "删除成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "删除失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "购物车ID格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("删除购物车商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "删除购物车商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 清空购物车
     */
    private void clearCart(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (!checkBuyerPermission(currentUser, response)) {
            return;
        }
        
        try {
            boolean success = cartService.clearCart(currentUser.getUserId());
            
            if (success) {
                ResponseUtil.sendJsonSuccess(response, "清空成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "清空失败");
            }
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("清空购物车失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "清空购物车失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前登录用户
     */
    private User getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute("currentUser");
        }
        return null;
    }

    /**
     * 检查用户权限（只有买家可以使用购物车）
     */
    private boolean checkBuyerPermission(User user, HttpServletResponse response) throws IOException {
        if (user == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return false;
        }
        if (!user.isBuyer()) {
            ResponseUtil.sendJsonError(response, 403, "只有买家可以使用购物车功能");
            return false;
        }
        return true;
    }
}
