<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.agricultural.model.User" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>农产品电商平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .cart-icon {
            position: relative;
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .search-form {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .item-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .item-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .item-description {
            color: #666;
            margin-bottom: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .item-price {
            font-size: 1.3rem;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }

        .item-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 15px;
        }

        .seller-info {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .credit-score {
            background-color: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .item-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .user-info {
            display: none;
            align-items: center;
            gap: 10px;
        }

        .user-name {
            font-weight: bold;
        }

        .auth-links {
            display: flex;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                gap: 10px;
            }

            .search-form {
                flex-direction: column;
            }

            .search-input {
                width: 100%;
            }

            .items-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <%
        // 获取当前用户信息
        User currentUser = (User) session.getAttribute("currentUser");
    %>

    <!-- 头部导航 -->
    <header class="header">
        <nav class="nav">
            <div class="logo">🌱 农产品电商平台</div>
            <div class="nav-links">
                <a href="index.jsp">首页</a>
                <% if (currentUser != null) { %>
                    <% if (currentUser.isBuyer()) { %>
                        <!-- 买家导航 -->
                        <a href="cart.jsp" class="cart-icon">
                            🛒 购物车
                            <span class="cart-count" id="cartCount">0</span>
                        </a>
                        <a href="my-orders.jsp">我的订单</a>
                    <% } else if (currentUser.isSeller()) { %>
                        <!-- 卖家导航 -->
                        <a href="my-items.jsp">我的商品</a>
                        <a href="publish-item.jsp">发布商品</a>
                        <a href="my-orders.jsp">我的订单</a>
                    <% } else if (currentUser.isAdmin()) { %>
                        <!-- 管理员导航 -->
                        <a href="admin-orders.jsp">所有订单管理</a>
                    <% } %>
                <% } %>
            </div>
            <div class="user-section">
                <% if (currentUser == null) { %>
                    <!-- 未登录时显示 -->
                    <div class="auth-links">
                        <a href="login.jsp" class="btn btn-primary btn-small">登录</a>
                        <a href="register.jsp" class="btn btn-success btn-small">注册</a>
                    </div>
                <% } else { %>
                    <!-- 已登录时显示 -->
                    <div class="user-info" style="display: flex;">
                        <span class="user-name"><%= currentUser.getUsername() %></span>
                        <%--<span class="credit-score"><%= currentUser.getCreditScore() %>分</span>--%>
                        <a href="javascript:logout()" class="btn btn-primary btn-small">退出</a>
                    </div>
                <% } %>
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main class="container">
        <!-- 搜索区域 -->
        <section class="search-section">
            <form class="search-form" onsubmit="searchItems(event)">
                <input type="text" class="search-input" id="searchKeyword" placeholder="搜索商品...">
                <button type="submit" class="btn btn-primary">搜索</button>
                <button type="button" class="btn btn-success" onclick="loadAllItems()">显示全部</button>
            </form>
        </section>

        <!-- 商品列表 -->
        <section class="items-section">
            <div id="itemsContainer">
                <div class="loading">正在加载商品...</div>
            </div>
        </section>
    </main>

    <script>
        // 当前用户ID
        const currentUserId = <%= currentUser != null ? currentUser.getUserId() : "null" %>;

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadAllItems();
            updateCartCount(); // 更新购物车数量
        });

        // 加载所有商品
        function loadAllItems() {
            document.getElementById('itemsContainer').innerHTML = '<div class="loading">正在加载商品...</div>';

            fetch('<%=request.getContextPath()%>/item/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayItems(data.data);
                    } else {
                        // 如果是未登录用户，仍然显示商品，但购买按钮会引导登录
                        document.getElementById('itemsContainer').innerHTML = '<div class="empty">暂无商品数据</div>';
                    }
                })
                .catch(error => {
                    console.error('加载商品失败:', error);
                    // 对于未登录用户，显示友好提示而不是错误信息
                    document.getElementById('itemsContainer').innerHTML = '<div class="empty">暂无商品数据，<a href="login.jsp">登录</a>后查看更多内容</div>';
                });
        }

        // 搜索商品
        function searchItems(event) {
            event.preventDefault();
            const keyword = document.getElementById('searchKeyword').value.trim();

            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            document.getElementById('itemsContainer').innerHTML = '<div class="loading">正在搜索...</div>';

            fetch('<%=request.getContextPath()%>/item/search?keyword=' + encodeURIComponent(keyword))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayItems(data.data);
                    } else {
                        document.getElementById('itemsContainer').innerHTML = '<div class="empty">搜索失败: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    console.error('搜索失败:', error);
                    document.getElementById('itemsContainer').innerHTML = '<div class="empty">搜索失败，请稍后重试</div>';
                });
        }

        // 显示商品列表
        function displayItems(items) {
            const container = document.getElementById('itemsContainer');

            if (!items || items.length === 0) {
                container.innerHTML = '<div class="empty">暂无商品</div>';
                return;
            }

            const itemsHtml = items.map(item => {
                let html = '<div class="item-card">';
                html += '<div class="item-title">' + escapeHtml(item.title) + '</div>';
                html += '<div class="item-description">' + escapeHtml(item.description || '暂无描述') + '</div>';
                html += '<div class="item-price">¥' + item.price + '</div>';
                html += '<div class="item-meta">';
                html += '<div class="seller-info">';
                html += '<span>卖家: ' + escapeHtml(item.seller ? item.seller.username : '未知') + '</span>';
                // if (item.seller) {
                //     html += '<span class="credit-score">' + item.seller.creditScore + '分</span>';
                // }
                html += '</div>';
                html += '<div class="item-time">' + formatDate(item.createTime) + '</div>';
                html += '</div>';
                html += '<div class="item-actions">';
                html += '<button class="btn btn-primary btn-small" onclick="viewItemDetail(' + item.itemId + ')">查看详情</button>';
                <% if (currentUser != null) { %>
                if (currentUserId !== item.sellerId) {
                    html += '<button class="btn btn-success btn-small" onclick="addToCart(' + item.itemId + ')">加入购物车</button>';
                }
                <% } %>
                html += '</div>';
                html += '</div>';
                return html;
            }).join('');

            container.innerHTML = '<div class="items-grid">' + itemsHtml + '</div>';
        }

        // 添加到购物车
        function addToCart(itemId) {
            <% if (currentUser == null) { %>
                if (confirm('添加商品需要先登录，是否前往登录页面？')) {
                    window.location.href = 'login.jsp?redirect=' + encodeURIComponent(window.location.href);
                }
                return;
            <% } %>

            fetch('<%=request.getContextPath()%>/cart/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'itemId=' + itemId + '&quantity=1'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('商品已添加到购物车！');
                    updateCartCount(); // 更新购物车数量
                } else {
                    alert('添加失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('添加到购物车失败:', error);
                alert('添加失败，请稍后重试');
            });
        }

        // 更新购物车数量
        function updateCartCount() {
            <% if (currentUser != null) { %>
            fetch('<%=request.getContextPath()%>/cart/count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const cartCount = document.getElementById('cartCount');
                        if (cartCount) {
                            cartCount.textContent = data.data;
                            cartCount.style.display = data.data > 0 ? 'flex' : 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error('获取购物车数量失败:', error);
                });
            <% } %>
        }

        // 查看商品详情
        function viewItemDetail(itemId) {
            window.location.href = 'item-detail.jsp?id=' + itemId;
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('<%=request.getContextPath()%>/user/logout')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('退出失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('退出失败:', error);
                        alert('退出失败，请稍后重试');
                    });
            }
        }

        // 工具函数：HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 工具函数：格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }
    </script>
</body>
</html>
